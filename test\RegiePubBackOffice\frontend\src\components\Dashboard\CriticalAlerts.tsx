"use client";
import { ClockIcon, TruckIcon, UserGroupIcon, ExclamationCircleIcon } from "@heroicons/react/24/outline"

export default function CriticalAlerts() {
  const alerts = [
    {
      id: 1,
      title: "Flotte disponible insuffisante",
      description: "Zone Sud • 68% dispo",
      icon: ClockIcon,
      bgColor: "bg-orange-50",
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600"
    },
    {
      id: 2,
      title: "Chauffeurs non validés",
      description: "5 chauffeurs en attente",
      icon: TruckIcon,
      bgColor: "bg-red-50",
      iconBg: "bg-red-100",
      iconColor: "text-red-600"
    },
    {
      id: 3,
      title: "Flotte disponible insuffisante",
      description: "Zone ouest • 50% dispo",
      icon: TruckIcon,
      bgColor: "bg-orange-50",
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600"
    },
    {
      id: 4,
      title: "Litiges non traités",
      description: "12 incidents ouverts",
      icon: ExclamationCircleIcon,
      bgColor: "bg-orange-50",
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600"
    },
    {
      id: 5,
      title: "Litiges non traités",
      description: "12 incidents ouverts",
      icon: ExclamationCircleIcon,
      bgColor: "bg-orange-50",
      iconBg: "bg-orange-100",
      iconColor: "text-orange-600"
    }
  ]

  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-900">Alertes critiques</h3>
      </div>
      
      <div className="space-y-2">
        {alerts.map((alert) => {
          const Icon = alert.icon
          return (
            <div key={alert.id} className={`${alert.bgColor} rounded-lg p-3 border border-gray-200`}>
              <div className="flex items-center space-x-3">
                <div className={`w-8 h-8 ${alert.iconBg} rounded-full flex items-center justify-center flex-shrink-0`}>
                  <Icon className={`w-4 h-4 ${alert.iconColor}`} />
                </div>
                
                <div className="flex-1 min-w-0">
                  <h4 className="text-xs font-medium text-gray-900 mb-0.5">
                    {alert.title}
                  </h4>
                  <p className="text-xs text-gray-600">
                    {alert.description}
                  </p>
                </div>
              </div>
            </div>
          )
        })}
      </div>
    </div>
  )
}


