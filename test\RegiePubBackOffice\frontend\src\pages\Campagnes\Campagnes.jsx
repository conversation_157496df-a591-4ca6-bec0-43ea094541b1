import TopBar from "@/components/Layout/TopBar"
import { ChevronRightIcon } from "@heroicons/react/24/outline"

export default function Campagnes() {
  // Sample campaign data organized by categories
  const campaigns = [
    {
      id: 1,
      name: "Campagne 1",
      drivers: [
        { id: 1, name: "<PERSON><PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 2, name: "<PERSON><PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 3, name: "<PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 4, name: "<PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 5, name: "<PERSON>", avatar: "/api/placeholder/32/32", status: "active" }
      ]
    },
    {
      id: 2,
      name: "Campagne 1",
      drivers: [
        { id: 6, name: "<PERSON><PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 7, name: "<PERSON><PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 8, name: "<PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 9, name: "<PERSON> <PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 10, name: "<PERSON> Flores", avatar: "/api/placeholder/32/32", status: "active" }
      ]
    },
    {
      id: 3,
      name: "Campagne 1",
      drivers: [
        { id: 11, name: "<PERSON> <PERSON>", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 12, name: "Ronald Richards", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 13, name: "Savannah Nguyen", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 14, name: "Eleanor Pena", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 15, name: "Esther Howard", avatar: "/api/placeholder/32/32", status: "active" }
      ]
    },
    {
      id: 4,
      name: "Campagne 1",
      drivers: [
        { id: 16, name: "Wade Warren", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 17, name: "Brooklyn Simmons", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 18, name: "Kristin Watson", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 19, name: "Jacob Jones", avatar: "/api/placeholder/32/32", status: "active" },
        { id: 20, name: "Cody Fisher", avatar: "/api/placeholder/32/32", status: "active" }
      ]
    }
  ]

  const getInitials = (name) => {
    return name.split(' ').map(n => n[0]).join('').toUpperCase()
  }

  const getAvatarColor = (name) => {
    const colors = [
      'bg-blue-500', 'bg-green-500', 'bg-yellow-500', 'bg-purple-500', 
      'bg-pink-500', 'bg-indigo-500', 'bg-red-500', 'bg-orange-500'
    ]
    const index = name.length % colors.length
    return colors[index]
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Bar */}
      <TopBar />
      
      {/* Main Content */}
      <div className="p-6">
        {/* Header */}
        <div className="mb-6">
          <h1 className="text-2xl font-semibold text-gray-900 mb-2">Campagnes</h1>
          <p className="text-sm text-gray-600">
            Configuration des tarifs par zones, formats de véhicules et créneaux horaires
          </p>
        </div>

        {/* Campaigns Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {campaigns.map((campaign) => (
            <div key={campaign.id} className="bg-white rounded-lg border border-gray-200 shadow-sm">
              {/* Campaign Header */}
              <div className="flex items-center justify-between p-4 border-b border-gray-200">
                <h3 className="text-sm font-medium text-gray-900">{campaign.name}</h3>
                <button className="text-blue-600 hover:text-blue-700 text-xs font-medium">
                  View All
                </button>
              </div>

              {/* Drivers List */}
              <div className="p-4">
                <div className="space-y-3">
                  {campaign.drivers.map((driver) => (
                    <div 
                      key={driver.id} 
                      className="flex items-center justify-between p-3 rounded-lg hover:bg-gray-50 transition-colors cursor-pointer group"
                    >
                      <div className="flex items-center space-x-3">
                        <div className={`w-8 h-8 ${getAvatarColor(driver.name)} rounded-full flex items-center justify-center`}>
                          <span className="text-white text-xs font-medium">
                            {getInitials(driver.name)}
                          </span>
                        </div>
                        <span className="text-sm font-medium text-gray-900">
                          {driver.name}
                        </span>
                      </div>
                      
                      <ChevronRightIcon className="w-4 h-4 text-gray-400 group-hover:text-gray-600 transition-colors" />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
