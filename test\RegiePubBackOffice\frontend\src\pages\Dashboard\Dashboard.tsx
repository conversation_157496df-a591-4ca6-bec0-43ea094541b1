import TopBar from "@/components/Layout/TopBar";
import KPICards from "@/components/Dashboard/KPICards";
import StatisticsCards from "@/components/Dashboard/StatisticsCards";
import ConversionChart from "@/components/Dashboard/ConversionChart";

import CriticalAlerts from "@/components/Dashboard/CriticalAlerts";
import AvailabilityTable from "@/components/Dashboard/AvailabilityTable";

export default function Dashboard() {
  return (
    <div className="min-h-screen bg-gray-50">
      {/* Top Bar */}
      <TopBar />
      
      {/* Main Content */}
      <div className="p-4">
        {/* KPI Cards Row */}
        <KPICards />
        
        {/* Statistics Cards Row */}
        <StatisticsCards />
        
        {/* Main Content Grid */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-4 mb-4">
          {/* Conversion Chart - Takes 2 columns */}
          <div className="lg:col-span-2">
            <ConversionChart />
          </div>
          
          {/* Right Column */}
          <div className="space-y-4">
            {/* Critical Alerts */}
            <CriticalAlerts />
          </div>
        </div>
        
        {/* Bottom Section - Availability Table */}
        <AvailabilityTable />
      </div>
    </div>
  );
}

