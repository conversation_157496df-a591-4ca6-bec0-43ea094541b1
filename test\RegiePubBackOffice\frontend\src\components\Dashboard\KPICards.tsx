"use client";;
import { TruckIcon, UserPlusIcon } from "@heroicons/react/24/outline";

export default function KPICards() {
  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-6">
      {/* Gérer les véhicules */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <TruckIcon className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-sm font-medium text-gray-900">Gérer les véhicules</h3>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Nouveau lot</span>
          <button className="bg-gray-800 text-white text-xs px-3 py-1.5 rounded-md hover:bg-gray-900 transition-colors">
            Gérer les véhicules
          </button>
        </div>
      </div>

      {/* Ajouter un chauffeur */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center space-x-3 mb-3">
          <div className="w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center">
            <UserPlusIcon className="w-6 h-6 text-blue-600" />
          </div>
          <h3 className="text-sm font-medium text-gray-900">Ajouter un chauffeur</h3>
        </div>
        <div className="flex items-center justify-between">
          <span className="text-xs text-gray-500">Nouveau lot</span>
          <button className="bg-blue-600 text-white text-xs px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors">
            Ajouter un chauffeur
          </button>
        </div>
      </div>

      {/* Couverture */}
      <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
        <div className="flex items-center justify-between mb-3">
          <h3 className="text-sm font-medium text-gray-900">Couverture</h3>
        </div>
        <div className="relative h-16 bg-gray-50 rounded-md overflow-hidden">
          {/* Simple map representation */}
          <div className="absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200">
            <div className="absolute top-2 left-2 w-3 h-2 bg-blue-600 rounded-sm opacity-80"></div>
            <div className="absolute top-3 right-3 w-4 h-3 bg-blue-500 rounded-sm opacity-70"></div>
            <div className="absolute bottom-2 left-3 w-2 h-2 bg-blue-700 rounded-sm opacity-90"></div>
            <div className="absolute bottom-2 right-2 w-3 h-2 bg-blue-400 rounded-sm opacity-60"></div>
          </div>
        </div>
      </div>
    </div>
  );
}


