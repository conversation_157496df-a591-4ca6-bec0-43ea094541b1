"use client";

export default function AvailabilityTable() {
  const availabilityData = [
    {
      zone: "Zone 1",
      availability: 85,
      status: "Disponible",
      vehicles: 24,
      color: "bg-green-500"
    },
    {
      zone: "Zone 2", 
      availability: 68,
      status: "Disponible",
      vehicles: 18,
      color: "bg-yellow-500"
    },
    {
      zone: "Zone 3",
      availability: 92,
      status: "Disponible", 
      vehicles: 31,
      color: "bg-green-500"
    },
    {
      zone: "Zone 4",
      availability: 45,
      status: "Critique",
      vehicles: 12,
      color: "bg-red-500"
    },
    {
      zone: "Zone 5",
      availability: 78,
      status: "Disponible",
      vehicles: 22,
      color: "bg-green-500"
    }
  ]

  const getStatusColor = (status) => {
    switch (status) {
      case "Disponible":
        return "text-green-600 bg-green-100"
      case "Critique":
        return "text-red-600 bg-red-100"
      default:
        return "text-gray-600 bg-gray-100"
    }
  }

  return (
    <div className="bg-white rounded-lg p-4 border border-gray-200 shadow-sm">
      <div className="flex items-center justify-between mb-4">
        <h3 className="text-sm font-medium text-gray-900">Flotte disponible par zone</h3>
      </div>

      <div className="space-y-3">
        <div className="grid grid-cols-4 gap-4 text-xs text-gray-500 pb-2 border-b border-gray-100">
          <span>Zone</span>
          <span>Disponibilité</span>
          <span>Statut</span>
          <span>Progression</span>
        </div>

        {availabilityData.map((item, index) => (
          <div key={index} className="grid grid-cols-4 gap-4 items-center py-2">
            <div className="flex items-center space-x-2">
              <div className={`w-2 h-2 rounded-full ${item.color}`}></div>
              <span className="text-xs font-medium text-gray-900">{item.zone}</span>
            </div>

            <div className="text-xs text-gray-600">
              {item.availability}%
            </div>

            <div>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>
                {item.status}
              </span>
            </div>

            <div className="flex items-center space-x-2">
              <div className="flex-1 bg-gray-200 rounded-full h-1.5">
                <div
                  className={`h-1.5 rounded-full ${item.color} transition-all duration-300`}
                  style={{ width: `${item.availability}%` }}
                ></div>
              </div>
              <span className="text-xs text-gray-500 min-w-[2rem]">{item.availability}%</span>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}


