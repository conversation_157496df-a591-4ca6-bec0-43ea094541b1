// Global type definitions

export interface MenuItem {
  name: string;
  icon: React.ComponentType<React.SVGProps<SVGSVGElement>>;
  href: string;
}

export interface Campaign {
  id: string;
  name: string;
  client: string;
  status: 'active' | 'paused' | 'completed';
  startDate: string;
  endDate: string;
  budget: number;
  spent: number;
}

export interface Driver {
  id: string;
  name: string;
  email: string;
  phone: string;
  status: 'active' | 'inactive' | 'suspended';
  vehicleId?: string;
  rating: number;
}

export interface Vehicle {
  id: string;
  model: string;
  licensePlate: string;
  status: 'available' | 'in_use' | 'maintenance';
  driverId?: string;
}

export type StatusType = 'active' | 'inactive' | 'maintenance' | 'available' | 'in_use' | 'paused' | 'completed' | 'suspended';
