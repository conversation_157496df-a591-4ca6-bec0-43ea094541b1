# 📚 Dashboard Application - Structure Réorganisée

## 🏗️ Nouvelle Structure du Projet

Le projet a été réorganisé pour une meilleure maintenabilité et séparation des responsabilités :

```
frontend/src/
├── app/                          # Routes Next.js (App Router)
│   ├── layout.js                 # Layout principal avec sidebar
│   ├── page.js                   # Page d'accueil (Dashboard)
│   ├── chauffeurs/
│   │   └── page.js              # Route /chauffeurs
│   ├── campagnes/
│   │   └── page.js              # Route /campagnes
│   └── globals.css              # Styles globaux
│
├── pages/                        # Pages principales de l'application
│   ├── Dashboard/
│   │   └── Dashboard.jsx        # Page Dashboard complète
│   ├── Chauffeurs/
│   │   └── Chauffeurs.jsx       # Page Chauffeurs
│   └── Campagnes/
│       └── Campagnes.jsx        # Page Campagnes
│
└── components/                   # Composants réutilisables
    ├── Layout/                   # Composants de mise en page
    │   ├── Sidebar.jsx          # Navigation latérale
    │   └── TopBar.jsx           # Barre supérieure
    │
    └── Dashboard/                # Composants spécifiques au Dashboard
        ├── KPICards.jsx         # Cartes KPI
        ├── StatisticsCards.jsx  # Cartes statistiques
        ├── ConversionChart.jsx  # Graphique de conversion
        ├── CoverageMap.jsx      # Carte de couverture
        ├── CriticalAlerts.jsx   # Alertes critiques
        └── AvailabilityTable.jsx # Tableau de disponibilité
```

## 🎯 Avantages de cette Structure

### **1. Séparation Claire des Responsabilités**
- **`/app`** : Gestion des routes Next.js uniquement
- **`/pages`** : Pages complètes de l'application
- **`/components`** : Composants réutilisables organisés par domaine

### **2. Maintenabilité Améliorée**
- Chaque page est dans son propre dossier
- Les composants sont groupés par fonctionnalité
- Import paths plus clairs et logiques

### **3. Réutilisabilité**
- Composants Layout partagés entre toutes les pages
- Composants Dashboard modulaires et réutilisables
- Structure extensible pour de nouvelles pages

### **4. Navigation Simplifiée**
- Routes Next.js dans `/app` font référence aux pages dans `/pages`
- Navigation centralisée dans le Sidebar
- URLs propres et SEO-friendly

## 🔧 Comment Ajouter une Nouvelle Page

### **1. Créer la Page**
```javascript
// src/pages/NouvelleSection/NouvelleSection.jsx
import TopBar from "@/components/Layout/TopBar"

export default function NouvelleSection() {
  return (
    <div className="min-h-screen bg-gray-50">
      <TopBar />
      <div className="p-6">
        <h1>Nouvelle Section</h1>
        {/* Contenu de la page */}
      </div>
    </div>
  )
}
```

### **2. Créer la Route**
```javascript
// src/app/nouvelle-section/page.js
import NouvelleSection from "@/pages/NouvelleSection/NouvelleSection"

export default function NouvelleSectionPage() {
  return <NouvelleSection />
}
```

### **3. Ajouter au Menu**
```javascript
// src/components/Layout/Sidebar.jsx
const menuItems = [
  // ... autres items
  { name: "Nouvelle Section", icon: IconName, href: "/nouvelle-section" },
]
```

## 📋 Pages Disponibles

| Page | Route | Description |
|------|-------|-------------|
| **Dashboard** | `/` | Tableau de bord principal avec KPIs, graphiques et alertes |
| **Chauffeurs** | `/chauffeurs` | Gestion des chauffeurs par campagnes |
| **Campagnes** | `/campagnes` | Configuration des campagnes et tarification |

## 🎨 Composants Disponibles

### **Layout Components**
- **Sidebar** : Navigation principale avec menu et profil utilisateur
- **TopBar** : Barre supérieure avec recherche et notifications

### **Dashboard Components**
- **KPICards** : Cartes d'actions rapides (Gérer véhicules, Ajouter chauffeur, etc.)
- **StatisticsCards** : Cartes de statistiques avec tendances
- **ConversionChart** : Graphique linéaire avec Chart.js
- **CoverageMap** : Représentation visuelle de la couverture
- **CriticalAlerts** : Liste des alertes critiques
- **AvailabilityTable** : Tableau de disponibilité par zone

## 🚀 Technologies Utilisées

- **Next.js 15.5.3** avec App Router
- **Tailwind CSS 4** pour le styling
- **Chart.js & React-Chart.js-2** pour les graphiques
- **Heroicons React** pour les icônes
- **React Hooks** pour la gestion d'état

---

**Version** : 2.0.0  
**Dernière mise à jour** : Janvier 2025  
**Structure** : Réorganisée et optimisée
