{"version": 3, "sources": [], "sections": [{"offset": {"line": 4, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/Layout/TopBar.tsx"], "sourcesContent": ["\"use client\";\nimport {\n  MagnifyingGlassIcon,\n  BellIcon,\n  UserCircleIcon,\n  ChevronDownIcon\n} from \"@heroicons/react/24/outline\";\n\nexport default function TopBar() {\n  return (\n    <div className=\"bg-white border-b border-gray-200 px-6 py-4\">\n      <div className=\"flex items-center justify-between\">\n        {/* Search Bar */}\n        <div className=\"flex-1 max-w-lg\">\n          <div className=\"relative\">\n            <div className=\"absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none\">\n              <MagnifyingGlassIcon className=\"h-5 w-5 text-gray-400\" />\n            </div>\n            <input\n              type=\"text\"\n              placeholder=\"Rechercher...\"\n              className=\"block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-lg leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-blue-500 focus:border-blue-500 sm:text-sm\"\n            />\n          </div>\n        </div>\n\n        {/* Right Side - Language, Notifications, Profile */}\n        <div className=\"flex items-center space-x-4\">\n          {/* Language Selector */}\n          <div className=\"flex items-center space-x-1\">\n            <div className=\"w-6 h-4 bg-blue-600 rounded-sm flex items-center justify-center\">\n              <span className=\"text-white text-xs font-bold\">FR</span>\n            </div>\n            <ChevronDownIcon className=\"w-4 h-4 text-gray-500\" />\n          </div>\n\n          {/* Notifications */}\n          <div className=\"relative\">\n            <button className=\"p-2 text-gray-400 hover:text-gray-500 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 rounded-full\">\n              <BellIcon className=\"h-6 w-6\" />\n              <span className=\"absolute top-0 right-0 block h-2 w-2 rounded-full bg-red-400 ring-2 ring-white\"></span>\n            </button>\n          </div>\n\n          {/* Profile */}\n          <div className=\"flex items-center space-x-3\">\n            <div className=\"flex items-center space-x-2\">\n              <UserCircleIcon className=\"h-8 w-8 text-gray-400\" />\n              <div className=\"hidden md:block\">\n                <p className=\"text-sm font-medium text-gray-700\">M. Ben Abdellah</p>\n                <p className=\"text-xs text-gray-500\">Administrateur</p>\n              </div>\n              <ChevronDownIcon className=\"w-4 h-4 text-gray-500\" />\n            </div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AAAA;AADA;;;AAQe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;kBACb,cAAA,8OAAC;YAAI,WAAU;;8BAEb,8OAAC;oBAAI,WAAU;8BACb,cAAA,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,4PAAmB;oCAAC,WAAU;;;;;;;;;;;0CAEjC,8OAAC;gCACC,MAAK;gCACL,aAAY;gCACZ,WAAU;;;;;;;;;;;;;;;;;8BAMhB,8OAAC;oBAAI,WAAU;;sCAEb,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;8CACb,cAAA,8OAAC;wCAAK,WAAU;kDAA+B;;;;;;;;;;;8CAEjD,8OAAC,gPAAe;oCAAC,WAAU;;;;;;;;;;;;sCAI7B,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAO,WAAU;;kDAChB,8OAAC,2NAAQ;wCAAC,WAAU;;;;;;kDACpB,8OAAC;wCAAK,WAAU;;;;;;;;;;;;;;;;;sCAKpB,8OAAC;4BAAI,WAAU;sCACb,cAAA,8OAAC;gCAAI,WAAU;;kDACb,8OAAC,6OAAc;wCAAC,WAAU;;;;;;kDAC1B,8OAAC;wCAAI,WAAU;;0DACb,8OAAC;gDAAE,WAAU;0DAAoC;;;;;;0DACjD,8OAAC;gDAAE,WAAU;0DAAwB;;;;;;;;;;;;kDAEvC,8OAAC,gPAAe;wCAAC,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAOzC", "debugId": null}}, {"offset": {"line": 202, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/Dashboard/KPICards.tsx"], "sourcesContent": ["\"use client\";;\nimport { TruckIcon, UserPlusIcon } from \"@heroicons/react/24/outline\";\n\nexport default function KPICards() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n      {/* Gérer les véhicules */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n            <TruckIcon className=\"w-6 h-6 text-blue-600\" />\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900\">Gérer les véhicules</h3>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-xs text-gray-500\">Nouveau lot</span>\n          <button className=\"bg-gray-800 text-white text-xs px-3 py-1.5 rounded-md hover:bg-gray-900 transition-colors\">\n            Gérer les véhicules\n          </button>\n        </div>\n      </div>\n\n      {/* Ajouter un chauffeur */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-3\">\n          <div className=\"w-10 h-10 bg-blue-100 rounded-lg flex items-center justify-center\">\n            <UserPlusIcon className=\"w-6 h-6 text-blue-600\" />\n          </div>\n          <h3 className=\"text-sm font-medium text-gray-900\">Ajouter un chauffeur</h3>\n        </div>\n        <div className=\"flex items-center justify-between\">\n          <span className=\"text-xs text-gray-500\">Nouveau lot</span>\n          <button className=\"bg-blue-600 text-white text-xs px-3 py-1.5 rounded-md hover:bg-blue-700 transition-colors\">\n            Ajouter un chauffeur\n          </button>\n        </div>\n      </div>\n\n      {/* Couverture */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center justify-between mb-3\">\n          <h3 className=\"text-sm font-medium text-gray-900\">Couverture</h3>\n        </div>\n        <div className=\"relative h-16 bg-gray-50 rounded-md overflow-hidden\">\n          {/* Simple map representation */}\n          <div className=\"absolute inset-0 bg-gradient-to-br from-blue-100 to-blue-200\">\n            <div className=\"absolute top-2 left-2 w-3 h-2 bg-blue-600 rounded-sm opacity-80\"></div>\n            <div className=\"absolute top-3 right-3 w-4 h-3 bg-blue-500 rounded-sm opacity-70\"></div>\n            <div className=\"absolute bottom-2 left-3 w-2 h-2 bg-blue-700 rounded-sm opacity-90\"></div>\n            <div className=\"absolute bottom-2 right-2 w-3 h-2 bg-blue-400 rounded-sm opacity-60\"></div>\n          </div>\n        </div>\n      </div>\n    </div>\n  );\n}\n\r\n\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AADA;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,8NAAS;oCAAC,WAAU;;;;;;;;;;;0CAEvB,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,8OAAC;gCAAO,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;0BAOlH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC,uOAAY;oCAAC,WAAU;;;;;;;;;;;0CAE1B,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAK,WAAU;0CAAwB;;;;;;0CACxC,8OAAC;gCAAO,WAAU;0CAA4F;;;;;;;;;;;;;;;;;;0BAOlH,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;kCACb,cAAA,8OAAC;4BAAG,WAAU;sCAAoC;;;;;;;;;;;kCAEpD,8OAAC;wBAAI,WAAU;kCAEb,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;8CACf,8OAAC;oCAAI,WAAU;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAM3B", "debugId": null}}, {"offset": {"line": 425, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/Dashboard/StatisticsCards.tsx"], "sourcesContent": ["\"use client\";\nimport { ArrowUpIcon, ArrowDownIcon } from \"@heroicons/react/24/solid\"\n\nexport default function StatisticsCards() {\n  return (\n    <div className=\"grid grid-cols-1 md:grid-cols-3 gap-4 mb-6\">\n      {/* Nombre de campagnes en cours */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <div className=\"w-10 h-10 bg-purple-100 rounded-lg flex items-center justify-center\">\n            <svg className=\"w-6 h-6 text-purple-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M11 5.882V19.24a1.76 1.76 0 01-3.417.592l-2.147-6.15M18 13a3 3 0 100-6M5.436 13.683A4.001 4.001 0 017 6h1.832c4.1 0 7.625-1.234 9.168-3v14c-1.543-1.766-5.067-3-9.168-3H7a3.988 3.988 0 01-1.564-.317z\" />\n            </svg>\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-xs text-gray-500 mb-1\">Nombre de campagnes en cours</p>\n            <p className=\"text-lg font-bold text-gray-900\">40,689</p>\n          </div>\n        </div>\n        <div className=\"flex items-center text-xs text-gray-500\">\n          <ArrowUpIcon className=\"w-3 h-3 text-green-500 mr-1\" />\n          <span className=\"text-green-500\">8.5%</span>\n          <span className=\"ml-1\">Up from yesterday</span>\n        </div>\n      </div>\n\n      {/* Revenus générés */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <div className=\"w-10 h-10 bg-green-100 rounded-lg flex items-center justify-center\">\n            <svg className=\"w-6 h-6 text-green-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M13 7h8m0 0v8m0-8l-8 8-4-4-6 6\" />\n            </svg>\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-xs text-gray-500 mb-1\">Revenus générés</p>\n            <p className=\"text-lg font-bold text-gray-900\">$89,000</p>\n          </div>\n        </div>\n        <div className=\"flex items-center text-xs text-gray-500\">\n          <ArrowDownIcon className=\"w-3 h-3 text-red-500 mr-1\" />\n          <span className=\"text-red-500\">4.3%</span>\n          <span className=\"ml-1\">Down from yesterday</span>\n        </div>\n      </div>\n\n      {/* Litiges ouverts / incidents signalés */}\n      <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n        <div className=\"flex items-center space-x-3 mb-2\">\n          <div className=\"w-10 h-10 bg-orange-100 rounded-lg flex items-center justify-center\">\n            <svg className=\"w-6 h-6 text-orange-600\" fill=\"none\" stroke=\"currentColor\" viewBox=\"0 0 24 24\">\n              <path strokeLinecap=\"round\" strokeLinejoin=\"round\" strokeWidth={2} d=\"M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z\" />\n            </svg>\n          </div>\n          <div className=\"flex-1\">\n            <p className=\"text-xs text-gray-500 mb-1\">Litiges ouverts / incidents signalés</p>\n            <p className=\"text-lg font-bold text-gray-900\">2040</p>\n          </div>\n        </div>\n        <div className=\"flex items-center text-xs text-gray-500\">\n          <ArrowUpIcon className=\"w-3 h-3 text-green-500 mr-1\" />\n          <span className=\"text-green-500\">1.8%</span>\n          <span className=\"ml-1\">Up from yesterday</span>\n        </div>\n      </div>\n    </div>\n  )\n}\n\r\n\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AADA;;;AAGe,SAAS;IACtB,qBACE,8OAAC;QAAI,WAAU;;0BAEb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;kCAGnD,8OAAC;wBAA<PERSON>,WAAU;;0CACb,8OAAC,kOAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;0CACjC,8OAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAAyB,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CAChF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;kCAGnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,wOAAa;gCAAC,WAAU;;;;;;0CACzB,8OAAC;gCAAK,WAAU;0CAAe;;;;;;0CAC/B,8OAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;0BAK3B,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAI,WAAU;0CACb,cAAA,8OAAC;oCAAI,WAAU;oCAA0B,MAAK;oCAAO,QAAO;oCAAe,SAAQ;8CACjF,cAAA,8OAAC;wCAAK,eAAc;wCAAQ,gBAAe;wCAAQ,aAAa;wCAAG,GAAE;;;;;;;;;;;;;;;;0CAGzE,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAE,WAAU;kDAA6B;;;;;;kDAC1C,8OAAC;wCAAE,WAAU;kDAAkC;;;;;;;;;;;;;;;;;;kCAGnD,8OAAC;wBAAI,WAAU;;0CACb,8OAAC,kOAAW;gCAAC,WAAU;;;;;;0CACvB,8OAAC;gCAAK,WAAU;0CAAiB;;;;;;0CACjC,8OAAC;gCAAK,WAAU;0CAAO;;;;;;;;;;;;;;;;;;;;;;;;AAKjC", "debugId": null}}, {"offset": {"line": 756, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/Dashboard/ConversionChart.tsx"], "sourcesContent": ["\"use client\";\nimport { Line } from 'react-chartjs-2'\nimport {\n  Chart as ChartJS,\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n} from 'chart.js'\n\nChartJS.register(\n  CategoryScale,\n  LinearScale,\n  PointElement,\n  LineElement,\n  Title,\n  Tooltip,\n  Legend,\n  Filler\n)\n\nexport default function ConversionChart() {\n  const data = {\n    labels: ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'],\n    datasets: [\n      {\n        label: 'Site Conversion',\n        data: [65, 78, 85, 72, 88, 95, 82],\n        borderColor: 'rgb(59, 130, 246)',\n        backgroundColor: 'rgba(59, 130, 246, 0.1)',\n        borderWidth: 3,\n        fill: true,\n        tension: 0.4,\n        pointBackgroundColor: 'rgb(59, 130, 246)',\n        pointBorderColor: '#fff',\n        pointBorderWidth: 2,\n        pointRadius: 6,\n        pointHoverRadius: 8,\n      }\n    ]\n  }\n\n  const options = {\n    responsive: true,\n    maintainAspectRatio: false,\n    plugins: {\n      legend: {\n        display: false,\n      },\n      title: {\n        display: false,\n      },\n      tooltip: {\n        backgroundColor: 'rgba(0, 0, 0, 0.8)',\n        titleColor: '#fff',\n        bodyColor: '#fff',\n        borderColor: 'rgba(59, 130, 246, 0.5)',\n        borderWidth: 1,\n        cornerRadius: 8,\n        displayColors: false,\n      }\n    },\n    scales: {\n      x: {\n        grid: {\n          display: false,\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: '#6B7280',\n          font: {\n            size: 12,\n          }\n        }\n      },\n      y: {\n        grid: {\n          color: 'rgba(107, 114, 128, 0.1)',\n          borderDash: [5, 5],\n        },\n        border: {\n          display: false,\n        },\n        ticks: {\n          color: '#6B7280',\n          font: {\n            size: 12,\n          },\n          callback: function(value) {\n            return value + '%'\n          }\n        },\n        min: 0,\n        max: 100,\n      }\n    },\n    interaction: {\n      intersect: false,\n      mode: 'index' as const,\n    },\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <div>\n          <h3 className=\"text-sm font-medium text-gray-900\">Nombre de conversion</h3>\n          <p className=\"text-xs text-gray-500\">Conversion rate by day, last week</p>\n        </div>\n        <div className=\"flex items-center space-x-4\">\n          <button className=\"text-xs text-blue-600 hover:text-blue-700\">Sync Recent</button>\n          <div className=\"flex items-center space-x-2\">\n            <div className=\"w-2 h-2 bg-blue-500 rounded-full\"></div>\n            <span className=\"text-xs text-gray-600\">Site Conversion</span>\n          </div>\n        </div>\n      </div>\n\n      <div className=\"h-64\">\n        <Line data={data} options={options} />\n      </div>\n    </div>\n  )\n}\n\n\n"], "names": [], "mappings": ";;;;;AACA;AACA;AAFA;;;;AAcA,qKAAO,CAAC,QAAQ,CACd,6KAAa,EACb,2KAAW,EACX,4KAAY,EACZ,2KAAW,EACX,qKAAK,EACL,uKAAO,EACP,sKAAM,EACN,sKAAM;AAGO,SAAS;IACtB,MAAM,OAAO;QACX,QAAQ;YAAC;YAAU;YAAU;YAAW;YAAa;YAAY;YAAU;SAAW;QACtF,UAAU;YACR;gBACE,OAAO;gBACP,MAAM;oBAAC;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;oBAAI;iBAAG;gBAClC,aAAa;gBACb,iBAAiB;gBACjB,aAAa;gBACb,MAAM;gBACN,SAAS;gBACT,sBAAsB;gBACtB,kBAAkB;gBAClB,kBAAkB;gBAClB,aAAa;gBACb,kBAAkB;YACpB;SACD;IACH;IAEA,MAAM,UAAU;QACd,YAAY;QACZ,qBAAqB;QACrB,SAAS;YACP,QAAQ;gBACN,SAAS;YACX;YACA,OAAO;gBACL,SAAS;YACX;YACA,SAAS;gBACP,iBAAiB;gBACjB,YAAY;gBACZ,WAAW;gBACX,aAAa;gBACb,aAAa;gBACb,cAAc;gBACd,eAAe;YACjB;QACF;QACA,QAAQ;YACN,GAAG;gBACD,MAAM;oBACJ,SAAS;gBACX;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;wBACJ,MAAM;oBACR;gBACF;YACF;YACA,GAAG;gBACD,MAAM;oBACJ,OAAO;oBACP,YAAY;wBAAC;wBAAG;qBAAE;gBACpB;gBACA,QAAQ;oBACN,SAAS;gBACX;gBACA,OAAO;oBACL,OAAO;oBACP,MAAM;wBACJ,MAAM;oBACR;oBACA,UAAU,SAAS,KAAK;wBACtB,OAAO,QAAQ;oBACjB;gBACF;gBACA,KAAK;gBACL,KAAK;YACP;QACF;QACA,aAAa;YACX,WAAW;YACX,MAAM;QACR;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;;0CACC,8OAAC;gCAAG,WAAU;0CAAoC;;;;;;0CAClD,8OAAC;gCAAE,WAAU;0CAAwB;;;;;;;;;;;;kCAEvC,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;gCAAO,WAAU;0CAA4C;;;;;;0CAC9D,8OAAC;gCAAI,WAAU;;kDACb,8OAAC;wCAAI,WAAU;;;;;;kDACf,8OAAC;wCAAK,WAAU;kDAAwB;;;;;;;;;;;;;;;;;;;;;;;;0BAK9C,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC,8JAAI;oBAAC,MAAM;oBAAM,SAAS;;;;;;;;;;;;;;;;;AAInC", "debugId": null}}, {"offset": {"line": 971, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/Dashboard/CriticalAlerts.tsx"], "sourcesContent": ["\"use client\";\nimport { ClockIcon, TruckIcon, UserGroupIcon, ExclamationCircleIcon } from \"@heroicons/react/24/outline\"\n\nexport default function CriticalAlerts() {\n  const alerts = [\n    {\n      id: 1,\n      title: \"Flotte disponible insuffisante\",\n      description: \"Zone Sud • 68% dispo\",\n      icon: ClockIcon,\n      bgColor: \"bg-orange-50\",\n      iconBg: \"bg-orange-100\",\n      iconColor: \"text-orange-600\"\n    },\n    {\n      id: 2,\n      title: \"Chauffeurs non validés\",\n      description: \"5 chauffeurs en attente\",\n      icon: TruckIcon,\n      bgColor: \"bg-red-50\",\n      iconBg: \"bg-red-100\",\n      iconColor: \"text-red-600\"\n    },\n    {\n      id: 3,\n      title: \"Flotte disponible insuffisante\",\n      description: \"Zone ouest • 50% dispo\",\n      icon: TruckIcon,\n      bgColor: \"bg-orange-50\",\n      iconBg: \"bg-orange-100\",\n      iconColor: \"text-orange-600\"\n    },\n    {\n      id: 4,\n      title: \"Litiges non traités\",\n      description: \"12 incidents ouverts\",\n      icon: ExclamationCircleIcon,\n      bgColor: \"bg-orange-50\",\n      iconBg: \"bg-orange-100\",\n      iconColor: \"text-orange-600\"\n    },\n    {\n      id: 5,\n      title: \"Litiges non traités\",\n      description: \"12 incidents ouverts\",\n      icon: ExclamationCircleIcon,\n      bgColor: \"bg-orange-50\",\n      iconBg: \"bg-orange-100\",\n      iconColor: \"text-orange-600\"\n    }\n  ]\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">Alertes critiques</h3>\n      </div>\n      \n      <div className=\"space-y-2\">\n        {alerts.map((alert) => {\n          const Icon = alert.icon\n          return (\n            <div key={alert.id} className={`${alert.bgColor} rounded-lg p-3 border border-gray-200`}>\n              <div className=\"flex items-center space-x-3\">\n                <div className={`w-8 h-8 ${alert.iconBg} rounded-full flex items-center justify-center flex-shrink-0`}>\n                  <Icon className={`w-4 h-4 ${alert.iconColor}`} />\n                </div>\n                \n                <div className=\"flex-1 min-w-0\">\n                  <h4 className=\"text-xs font-medium text-gray-900 mb-0.5\">\n                    {alert.title}\n                  </h4>\n                  <p className=\"text-xs text-gray-600\">\n                    {alert.description}\n                  </p>\n                </div>\n              </div>\n            </div>\n          )\n        })}\n      </div>\n    </div>\n  )\n}\n\r\n\r\n"], "names": [], "mappings": ";;;;;AACA;AAAA;AAAA;AADA;;;AAGe,SAAS;IACtB,MAAM,SAAS;QACb;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8NAAS;YACf,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8NAAS;YACf,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,8NAAS;YACf,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YACP,aAAa;YACb,MAAM,kQAAqB;YAC3B,SAAS;YACT,QAAQ;YACR,WAAW;QACb;QACA;YACE,IAAI;YACJ,OAAO;YAC<PERSON>,aAAa;YACb,MAAM,kQAAqB;YAC3B,SAAS;YACT,QAAQ;YACR,WAAW;QACb;KACD;IAED,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAoC;;;;;;;;;;;0BAGpD,8OAAC;gBAAI,WAAU;0BACZ,OAAO,GAAG,CAAC,CAAC;oBACX,MAAM,OAAO,MAAM,IAAI;oBACvB,qBACE,8OAAC;wBAAmB,WAAW,GAAG,MAAM,OAAO,CAAC,sCAAsC,CAAC;kCACrF,cAAA,8OAAC;4BAAI,WAAU;;8CACb,8OAAC;oCAAI,WAAW,CAAC,QAAQ,EAAE,MAAM,MAAM,CAAC,4DAA4D,CAAC;8CACnG,cAAA,8OAAC;wCAAK,WAAW,CAAC,QAAQ,EAAE,MAAM,SAAS,EAAE;;;;;;;;;;;8CAG/C,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAG,WAAU;sDACX,MAAM,KAAK;;;;;;sDAEd,8OAAC;4CAAE,WAAU;sDACV,MAAM,WAAW;;;;;;;;;;;;;;;;;;uBAXhB,MAAM,EAAE;;;;;gBAiBtB;;;;;;;;;;;;AAIR", "debugId": null}}, {"offset": {"line": 1124, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Desktop/test/RegiePubBackOffice/frontend/src/components/Dashboard/AvailabilityTable.tsx"], "sourcesContent": ["\"use client\";\n\nexport default function AvailabilityTable() {\n  const availabilityData = [\n    {\n      zone: \"Zone 1\",\n      availability: 85,\n      status: \"Disponible\",\n      vehicles: 24,\n      color: \"bg-green-500\"\n    },\n    {\n      zone: \"Zone 2\", \n      availability: 68,\n      status: \"Disponible\",\n      vehicles: 18,\n      color: \"bg-yellow-500\"\n    },\n    {\n      zone: \"Zone 3\",\n      availability: 92,\n      status: \"Disponible\", \n      vehicles: 31,\n      color: \"bg-green-500\"\n    },\n    {\n      zone: \"Zone 4\",\n      availability: 45,\n      status: \"Critique\",\n      vehicles: 12,\n      color: \"bg-red-500\"\n    },\n    {\n      zone: \"Zone 5\",\n      availability: 78,\n      status: \"Disponible\",\n      vehicles: 22,\n      color: \"bg-green-500\"\n    }\n  ]\n\n  const getStatusColor = (status) => {\n    switch (status) {\n      case \"Disponible\":\n        return \"text-green-600 bg-green-100\"\n      case \"Critique\":\n        return \"text-red-600 bg-red-100\"\n      default:\n        return \"text-gray-600 bg-gray-100\"\n    }\n  }\n\n  return (\n    <div className=\"bg-white rounded-lg p-4 border border-gray-200 shadow-sm\">\n      <div className=\"flex items-center justify-between mb-4\">\n        <h3 className=\"text-sm font-medium text-gray-900\">Flotte disponible par zone</h3>\n      </div>\n\n      <div className=\"space-y-3\">\n        <div className=\"grid grid-cols-4 gap-4 text-xs text-gray-500 pb-2 border-b border-gray-100\">\n          <span>Zone</span>\n          <span>Disponibilité</span>\n          <span>Statut</span>\n          <span>Progression</span>\n        </div>\n\n        {availabilityData.map((item, index) => (\n          <div key={index} className=\"grid grid-cols-4 gap-4 items-center py-2\">\n            <div className=\"flex items-center space-x-2\">\n              <div className={`w-2 h-2 rounded-full ${item.color}`}></div>\n              <span className=\"text-xs font-medium text-gray-900\">{item.zone}</span>\n            </div>\n\n            <div className=\"text-xs text-gray-600\">\n              {item.availability}%\n            </div>\n\n            <div>\n              <span className={`px-2 py-1 rounded-full text-xs font-medium ${getStatusColor(item.status)}`}>\n                {item.status}\n              </span>\n            </div>\n\n            <div className=\"flex items-center space-x-2\">\n              <div className=\"flex-1 bg-gray-200 rounded-full h-1.5\">\n                <div\n                  className={`h-1.5 rounded-full ${item.color} transition-all duration-300`}\n                  style={{ width: `${item.availability}%` }}\n                ></div>\n              </div>\n              <span className=\"text-xs text-gray-500 min-w-[2rem]\">{item.availability}%</span>\n            </div>\n          </div>\n        ))}\n      </div>\n    </div>\n  )\n}\n\r\n\r\n"], "names": [], "mappings": ";;;;;AAAA;;AAEe,SAAS;IACtB,MAAM,mBAAmB;QACvB;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;QACA;YACE,MAAM;YACN,cAAc;YACd,QAAQ;YACR,UAAU;YACV,OAAO;QACT;KACD;IAED,MAAM,iBAAiB,CAAC;QACtB,OAAQ;YACN,KAAK;gBA<PERSON>,OAAO;YACT,KAAK;g<PERSON><PERSON>,OAAO;YACT;gBACE,OAAO;QACX;IACF;IAEA,qBACE,8OAAC;QAAI,WAAU;;0BACb,8OAAC;gBAAI,WAAU;0BACb,cAAA,8OAAC;oBAAG,WAAU;8BAAoC;;;;;;;;;;;0BAGpD,8OAAC;gBAAI,WAAU;;kCACb,8OAAC;wBAAI,WAAU;;0CACb,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;0CACN,8OAAC;0CAAK;;;;;;;;;;;;oBAGP,iBAAiB,GAAG,CAAC,CAAC,MAAM,sBAC3B,8OAAC;4BAAgB,WAAU;;8CACzB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAW,CAAC,qBAAqB,EAAE,KAAK,KAAK,EAAE;;;;;;sDACpD,8OAAC;4CAAK,WAAU;sDAAqC,KAAK,IAAI;;;;;;;;;;;;8CAGhE,8OAAC;oCAAI,WAAU;;wCACZ,KAAK,YAAY;wCAAC;;;;;;;8CAGrB,8OAAC;8CACC,cAAA,8OAAC;wCAAK,WAAW,CAAC,2CAA2C,EAAE,eAAe,KAAK,MAAM,GAAG;kDACzF,KAAK,MAAM;;;;;;;;;;;8CAIhB,8OAAC;oCAAI,WAAU;;sDACb,8OAAC;4CAAI,WAAU;sDACb,cAAA,8OAAC;gDACC,WAAW,CAAC,mBAAmB,EAAE,KAAK,KAAK,CAAC,4BAA4B,CAAC;gDACzE,OAAO;oDAAE,OAAO,GAAG,KAAK,YAAY,CAAC,CAAC,CAAC;gDAAC;;;;;;;;;;;sDAG5C,8OAAC;4CAAK,WAAU;;gDAAsC,KAAK,YAAY;gDAAC;;;;;;;;;;;;;;2BAvBlE;;;;;;;;;;;;;;;;;AA8BpB", "debugId": null}}]}